apply plugin: 'org.shipkit.shipkit-auto-version'
apply plugin: 'org.shipkit.shipkit-changelog'
apply plugin: 'org.shipkit.shipkit-github-release'

tasks.named('generateChangelog') {
    previousRevision = project.ext.'shipkit-auto-version.previous-tag'
    githubToken = System.getenv('GITHUB_TOKEN')
    repository = 'mockito/mockito'
}

tasks.named("githubRelease") {
    def genTask = tasks.named("generateChangelog").get()
    dependsOn genTask
    repository = genTask.repository
    changelog = genTask.outputFile
    newTagRevision = System.getenv("GITHUB_SHA")
    githubToken = System.getenv("GITHUB_TOKEN")
}

apply plugin: 'io.github.gradle-nexus.publish-plugin'
nexusPublishing {
    packageGroup = "org.mockito"
    repositoryDescription = provider { "Mockito " + project.version }

    repositories {
        if (System.getenv('NEXUS_TOKEN_PWD')) {
            sonatype {
                // Publishing to: https://s01.oss.sonatype.org (faster instance)
                nexusUrl = uri('https://s01.oss.sonatype.org/service/local/')
                snapshotRepositoryUrl = uri('https://s01.oss.sonatype.org/content/repositories/snapshots/')

                username = System.getenv('NEXUS_TOKEN_USER')
                password = System.getenv('NEXUS_TOKEN_PWD')
            }
        }
    }
}

def isSnapshot = version.endsWith("-SNAPSHOT")
if (isSnapshot) {
    tasks.named("githubRelease") {
        //snapshot versions do not produce changelog / GitHub releases
        enabled = false
    }
    tasks.named("closeAndReleaseStagingRepository") {
        //snapshot binaries are available in Sonatype without the need to close the staging repo
        enabled = false
    }
}

tasks.register("releaseSummary") {
    doLast {
        if (isSnapshot) {
            println "RELEASE SUMMARY\n" +
                "  SNAPSHOTS released to: https://s01.oss.sonatype.org/content/repositories/snapshots/org/mockito/mockito-core\n" +
                "  Release to Maven Central: SKIPPED FOR SNAPSHOTS\n" +
                "  Github releases: SKIPPED FOR SNAPSHOTS"
        } else {
            println "RELEASE SUMMARY\n" +
                "  Release to Maven Central (available in few hours): https://repo1.maven.org/maven2/org/mockito/mockito-core/\n" +
                "  Github releases: https://github.com/mockito/mockito/releases"
        }
    }
}
