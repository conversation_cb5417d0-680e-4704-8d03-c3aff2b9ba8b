<?xml version="1.0"?>
<!DOCTYPE module PUBLIC
    "-//Puppy Crawl//DTD Check Configuration 1.2//EN"
    "https://checkstyle.org/dtds/configuration_1_2.dtd">
<module name="Checker">
    <!--
     Different editors and IDEs may insert or remove trailing
     whitespaces at the end of source lines or newline characters
     at the end of source files.

     This changes have no actual meaning, but may cause visual spam
     when [re]viewing diffs.
     Having checkstyles rules to strictly enforce the defacto
     standards of the project helps avoid such issues and make the
     review process more efficient.

     See also https://github.com/mockito/mockito/issues/903
     -->
    <module name="BeforeExecutionExclusionFileFilter">
        <property name="fileNamePattern" value="module\-info\.java$" />
    </module>
    <module name="NewlineAtEndOfFile"/>
    <module name="RegexpSingleline">
        <!-- \s matches whitespace character, $ matches end of line. -->
        <property name="format" value="\s+$"/>
        <property name="message" value="Line has trailing spaces."/>
    </module>
    <module name="TreeWalker" >
        <module name="UnusedImports">
            <property name="processJavadoc" value="true"/>
        </module>
    </module>
    <module name="RegexpHeader">
        <property name="headerFile" value="${config_loc}/java.header"/>
        <property name="fileExtensions" value="java"/>
    </module>
</module>
