<!-- Hey,
Thanks for the contribution, this is awesome.
As you may have read, project members have somehow an opinionated view on what and how should be
<PERSON><PERSON><PERSON>, e.g. we don't want mockito to be a feature bloat.
There may be a thorough review, with feedback -> code change loop.
-->
<!--
If you have a suggestion for this template you can fix it in the .github/PULL_REQUEST_TEMPLATE.md file
-->
## Checklist

 - [ ] Read the [contributing guide](https://github.com/mockito/mockito/blob/main/.github/CONTRIBUTING.md)
 - [ ] PR should be motivated, i.e. what does it fix, why, and if relevant how
 - [ ] If possible / relevant include an example in the description, that could help all readers
       including project members to get a better picture of the change
 - [ ] Avoid other runtime dependencies
 - [ ] Meaningful commit history ; intention is important please rebase your commit history so that each
       commit is meaningful and help the people that will explore a change in 2 years
 - [ ] The pull request follows coding style
 - [ ] Mention `Fixes #<issue number>` in the description _if relevant_
 - [ ] At least one commit should mention `Fixes #<issue number>` _if relevant_

