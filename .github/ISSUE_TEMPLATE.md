> Hey,
>
> First thanks for reporting, in order to help us to classify issue can you make sure the following check boxes are checked ?
>
> If this is about mockito usage, the better way is to reach out to
>
>  - stackoverflow : https://stackoverflow.com/questions/tagged/mockito
>  - the mailing-list  : https://groups.google.com/forum/#!forum/mockito / <EMAIL>
>    (Note mailing-list is moderated to avoid spam)
>
> _This block can be removed_
> _Something wrong in the template fix it here `.github/ISSUE_TEMPLATE.md`


check that

 - [ ] The mockito message in the stacktrace have useful information, but it didn't help
 - [ ] The problematic code (if that's possible) is copied here;
       Note that some configuration are impossible to mock via Mockito
 - [ ] Provide versions (mockito / jdk / os / any other relevant information)
 - [ ] Provide a [Short, Self Contained, Correct (Compilable), Example](http://sscce.org) of the issue
       (same as any question on stackoverflow.com)
 - [ ] Read the [contributing guide](https://github.com/mockito/mockito/blob/main/.github/CONTRIBUTING.md)


